#!/usr/bin/env python3
"""
15-Year Historical Data Loading Script for All NSE Symbols
Loads 15 years of 1-minute data for all symbols from NSE_CM.csv for nightly runs.
Uses Fyers API integration from auth folder for real data loading.
"""

import sys
import os
import argparse
import signal
import time
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional


# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Add auth folder to path for Fyers integration
auth_path = project_root / "auth"
sys.path.insert(0, str(auth_path))

from app.core.logging import get_logger
from app.database.connection import get_db, check_database_connection
from app.services.data_service import DataService
from app.services.symbol_mapping_service import SymbolMappingService

# Import Fyers integration from auth folder
from auth.config_loader import get_config
from auth.fyers_config import FyersConfig
from auth.fyers_connect import FyersConnect

logger = get_logger(__name__)

class AllSymbolsDataManager:
    """Manager for loading 15-year historical data for all NSE symbols using Fyers API."""

    def __init__(self):
        """Initialize the manager."""
        self.db = None
        self.data_service = None
        self.symbol_service = None
        self.fyers_client = None
        self.interrupted = False

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle interrupt signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.interrupted = True
    
    def initialize_services(self) -> bool:
        """Initialize all required services including Fyers API."""
        try:
            # Check database connection
            if not check_database_connection():
                logger.error("Database connection failed")
                return False

            # Initialize database session
            self.db = next(get_db())

            # Initialize services
            self.data_service = DataService(self.db)
            self.symbol_service = SymbolMappingService()

            # Initialize Fyers API connection
            logger.info("Initializing Fyers API connection...")
            # Use config.yaml from project root
            config_path = project_root / "config.yaml"
            config_loader = get_config(str(config_path))
            config = config_loader._config  # Get the actual config dictionary
            fyers_config = FyersConfig(config['general']['env_path'])
            self.fyers_client = FyersConnect(fyers_config)

            # Login to Fyers
            if not self.fyers_client.login():
                logger.error("Fyers API login failed")
                return False

            logger.info("✓ Services initialized successfully")
            logger.info("✓ Fyers API connection established")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    def get_symbols_to_load(self, symbol_filter: str = None) -> List[str]:
        """Get list of symbols to load data for."""
        try:
            logger.info("📋 Getting symbols to load...")

            if symbol_filter == "nifty50":
                # Load NIFTY 50 constituents + major indices
                symbols = ["NIFTY", "BANKNIFTY", "FINNIFTY"]
                # For now, just use the major indices
                # TODO: Add NIFTY 50 constituents from database
                logger.info(f"Selected major indices: {len(symbols)} symbols")

            elif symbol_filter == "indices":
                # Load only major indices
                symbols = ["NIFTY", "BANKNIFTY", "FINNIFTY"]
                logger.info(f"Selected indices: {len(symbols)} symbols")

            elif symbol_filter == "all":
                # For now, load major indices only
                # TODO: Implement full symbol loading from NSE_CM.csv
                symbols = ["NIFTY", "BANKNIFTY", "FINNIFTY"]
                logger.info(f"Selected all available symbols: {len(symbols)} symbols")

            else:
                # Default to NIFTY only
                symbols = ["NIFTY"]
                logger.info("Selected NIFTY only")

            return symbols

        except Exception as e:
            logger.error(f"Error getting symbols to load: {e}")
            return []
    
    def calculate_date_range(self, years: int = 15) -> tuple:
        """Calculate start and end dates for data loading."""
        end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = end_date - timedelta(days=years * 365)
        
        logger.info(f"Date range: {start_date.date()} to {end_date.date()} ({years} years)")
        return start_date, end_date
    
    def load_symbol_data(self, symbol: str, start_date: datetime, end_date: datetime) -> bool:
        """Load 15-year data for a single symbol using Fyers API."""
        try:
            logger.info(f"🔄 Loading data for {symbol}...")

            # Ensure symbol exists in database
            symbol_obj = self.data_service.get_symbol_by_name(symbol)
            if not symbol_obj:
                # Create symbol if it doesn't exist
                symbol_data = {
                    'symbol': symbol,
                    'name': symbol,
                    'market_type': 'INDEX' if symbol in ['NIFTY', 'BANKNIFTY', 'FINNIFTY'] else 'EQUITY',
                    'exchange': 'NSE',
                    'is_active': True
                }
                symbol_obj = self.data_service.create_symbol(symbol_data)
                logger.info(f"   Created symbol: {symbol}")

            # Check existing data count
            latest_data = self.data_service.get_latest_ohlcv(symbol, count=10)
            if latest_data:
                logger.info(f"   Existing records found: {len(latest_data)} latest records")

            # Load data in chunks to respect API limits (100 days max per request)
            current_date = start_date
            total_records = 0
            chunk_size_days = 90  # Stay within 100-day limit

            while current_date < end_date and not self.interrupted:
                chunk_end = min(current_date + timedelta(days=chunk_size_days), end_date)

                logger.info(f"   Loading chunk: {current_date.date()} to {chunk_end.date()}")

                # Fetch OHLC data from Fyers API
                df = self.fyers_client.get_ohlc_data(
                    symbol=symbol,
                    interval="1",  # 1-minute data
                    start_date=current_date.strftime('%Y-%m-%d'),
                    end_date=chunk_end.strftime('%Y-%m-%d')
                )

                if not df.empty:
                    # Convert index from UTC to Asia/Kolkata and remove timezone info
                    import pytz
                    df.index = df.index.tz_localize('UTC').tz_convert('Asia/Kolkata').tz_localize(None)
                    
                    # Convert DataFrame to list of dictionaries for storage
                    ohlcv_data = []
                    for idx, row in df.iterrows():
                        ohlcv_data.append({
                            'timestamp': idx,
                            'open': float(row['open']),
                            'high': float(row['high']),
                            'low': float(row['low']),
                            'close': float(row['close']),
                            'volume': int(row['volume'])
                        })

                    # Store data in database
                    success = self.data_service.store_ohlcv_data(symbol, ohlcv_data, upsert=True)
                    if success:
                        total_records += len(ohlcv_data)
                        logger.info(f"   Stored {len(ohlcv_data)} records for chunk")
                    else:
                        logger.error(f"   Failed to store data for chunk")
                        return False
                else:
                    logger.warning(f"   No data received for chunk: {current_date.date()} to {chunk_end.date()}")

                # Move to next chunk
                current_date = chunk_end + timedelta(days=1)

                # Rate limiting between chunks
                time.sleep(1)

            logger.info(f"   ✅ {symbol}: Loaded {total_records} total records")
            return True

        except Exception as e:
            logger.error(f"Error loading data for {symbol}: {e}")
            return False

    def display_latest_ohlcv_data(self, symbol: str, count: int = 10) -> None:
        """Display latest OHLCV data for a symbol."""
        try:
            logger.info(f"📊 Latest {count} OHLCV records for {symbol}:")

            # Get latest data from database
            latest_data = self.data_service.get_latest_ohlcv(symbol, count=count)

            if not latest_data:
                logger.info(f"   No data found for {symbol}")
                return

            # Display in table format
            logger.info("   " + "="*80)
            logger.info(f"   {'Timestamp':<20} {'Open':<10} {'High':<10} {'Low':<10} {'Close':<10} {'Volume':<12}")
            logger.info("   " + "="*80)

            for record in latest_data:
                timestamp_str = record.timestamp.strftime('%Y-%m-%d %H:%M')
                logger.info(f"   {timestamp_str:<20} {record.open:<10.2f} {record.high:<10.2f} "
                          f"{record.low:<10.2f} {record.close:<10.2f} {record.volume:<12,}")

            logger.info("   " + "="*80)

        except Exception as e:
            logger.error(f"Error displaying latest OHLCV data for {symbol}: {e}")

    def get_symbol_date_range(self, symbol: str) -> Optional[Dict]:
        """Get the date range of available data for a symbol."""
        try:
            # Get data statistics which includes date range and record count
            stats = self.data_service.get_data_statistics(symbol)
            if stats and stats['data_range']['start'] and stats['data_range']['end']:
                return {
                    'first_timestamp': stats['data_range']['start'],
                    'last_timestamp': stats['data_range']['end'],
                    'total_records': stats['total_records']
                }
            return None
        except Exception as e:
            logger.error(f"Error getting date range for {symbol}: {e}")
            return None

    def display_data_ranges(self, symbols: List[str]) -> None:
        """Display date ranges for all specified symbols."""
        try:
            logger.info("📅 DATA AVAILABILITY SUMMARY")
            logger.info("=" * 100)
            logger.info(f"{'Symbol':<15} {'First Record':<20} {'Last Record':<20} {'Total Days':<12} {'Total Records':<15}")
            logger.info("=" * 100)

            total_symbols_with_data = 0
            
            for symbol in symbols:
                if self.interrupted:
                    break
                    
                # Get symbol object
                symbol_obj = self.data_service.get_symbol_by_name(symbol)
                if not symbol_obj:
                    logger.info(f"{symbol:<15} {'No symbol found':<20} {'-':<20} {'-':<12} {'-':<15}")
                    continue

                # Get date range and record count
                date_range = self.get_symbol_date_range(symbol)
                if date_range and date_range['first_timestamp'] and date_range['last_timestamp']:
                    first_date = date_range['first_timestamp']
                    last_date = date_range['last_timestamp']
                    total_records = date_range['total_records']
                    
                    # Calculate total days
                    total_days = (last_date.date() - first_date.date()).days + 1
                    
                    first_str = first_date.strftime('%Y-%m-%d %H:%M')
                    last_str = last_date.strftime('%Y-%m-%d %H:%M')
                    
                    logger.info(f"{symbol:<15} {first_str:<20} {last_str:<20} {total_days:<12,} {total_records:<15,}")
                    total_symbols_with_data += 1
                else:
                    logger.info(f"{symbol:<15} {'No data found':<20} {'-':<20} {'-':<12} {'-':<15}")

            logger.info("=" * 100)
            logger.info(f"Summary: {total_symbols_with_data}/{len(symbols)} symbols have data available")
            logger.info("=" * 100)

        except Exception as e:
            logger.error(f"Error displaying data ranges: {e}")

    def run_bulk_load(self, symbols: List[str], years: int = 15) -> Dict[str, bool]:
        """Run bulk data loading for multiple symbols."""
        try:
            start_date, end_date = self.calculate_date_range(years)

            logger.info(f"🚀 Starting bulk data loading for {len(symbols)} symbols...")
            logger.info("=" * 80)

            results = {}
            successful_loads = 0
            failed_loads = 0

            for i, symbol in enumerate(symbols, 1):
                if self.interrupted:
                    logger.info("⚠️  Operation interrupted by user")
                    break

                logger.info(f"\n📊 Processing symbol {i}/{len(symbols)}: {symbol}")

                success = self.load_symbol_data(symbol, start_date, end_date)
                results[symbol] = success

                if success:
                    successful_loads += 1
                    # Display latest 10 OHLCV records for successful loads
                    self.display_latest_ohlcv_data(symbol, count=10)
                else:
                    failed_loads += 1

                # Progress summary
                logger.info(f"   Progress: {i}/{len(symbols)} symbols processed")
                logger.info(f"   Success: {successful_loads}, Failed: {failed_loads}")

                # Rate limiting between symbols
                if i < len(symbols):
                    time.sleep(2)  # 2-second delay between symbols

            # Final summary
            logger.info("\n" + "=" * 80)
            logger.info("📈 BULK LOADING SUMMARY")
            logger.info("=" * 80)
            logger.info(f"Total symbols processed: {len(results)}")
            logger.info(f"Successful loads: {successful_loads}")
            logger.info(f"Failed loads: {failed_loads}")
            logger.info(f"Success rate: {(successful_loads/len(results)*100):.1f}%")

            return results

        except Exception as e:
            logger.error(f"Error in bulk loading: {e}")
            return {}
    
    def cleanup(self):
        """Cleanup resources."""
        if self.db:
            self.db.close()
            logger.info("✓ Database connection closed")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Load 15 years of historical data for all NSE symbols")
    parser.add_argument("--years", type=int, default=15, 
                       help="Number of years to load (default: 15)")
    parser.add_argument("--symbols", choices=["nifty50", "indices", "all", "nifty"], default="nifty50",
                       help="Symbol set to load (default: nifty50)")
    parser.add_argument("--dry-run", action="store_true",
                       help="Show symbols that would be loaded without actually loading")
    parser.add_argument("--view", action="store_true",
                       help="Display date range of data available in database for specified symbols")
    
    args = parser.parse_args()
    
    if args.view:
        logger.info("📅 Data Availability Viewer for NSE Symbols")
    else:
        logger.info("🚀 15-Year Historical Data Loading Script for All NSE Symbols")
    logger.info("=" * 80)
    
    manager = AllSymbolsDataManager()
    
    try:
        # Initialize services
        logger.info("Step 1: Initializing services...")
        if not manager.initialize_services():
            return False
        
        # Get symbols to load
        logger.info("\nStep 2: Getting symbols to load...")
        symbols = manager.get_symbols_to_load(args.symbols)
        
        if not symbols:
            logger.error("No symbols found to load")
            return False
        
        if args.dry_run:
            logger.info(f"\n📋 DRY RUN - Would load data for {len(symbols)} symbols:")
            for i, symbol in enumerate(symbols, 1):
                logger.info(f"  {i:3d}. {symbol}")
            return True
        
        if args.view:
            logger.info(f"\n📅 Displaying data availability for {len(symbols)} symbols...")
            manager.display_data_ranges(symbols)
            return True
        
        # Starting automated data loading for nightly runs
        logger.info(f"\n🚀 Starting automated load of {args.years} years of data for {len(symbols)} symbols")
        logger.info("This operation may take several hours to complete.")
        
        # Start bulk loading
        logger.info("\nStep 3: Starting bulk data loading...")
        results = manager.run_bulk_load(symbols, args.years)
        
        # Show final results
        successful_symbols = [s for s, success in results.items() if success]
        failed_symbols = [s for s, success in results.items() if not success]
        
        if successful_symbols:
            logger.info(f"\n✅ Successfully loaded data for {len(successful_symbols)} symbols")
        
        if failed_symbols:
            logger.info(f"\n❌ Failed to load data for {len(failed_symbols)} symbols:")
            for symbol in failed_symbols:
                logger.info(f"   - {symbol}")
        
        return len(failed_symbols) == 0
        
    except KeyboardInterrupt:
        logger.info("\n⚠️  Operation interrupted by user")
        return False
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return False
    finally:
        manager.cleanup()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
